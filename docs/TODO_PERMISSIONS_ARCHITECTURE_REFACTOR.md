# Permission System Architecture Refactor - Critical Issues & Recommendations

## Executive Summary

Our current permission system has evolved into a **three-layer architecture** with significant inconsistencies and gaps:

1. **Plan-based Permissions** (Frontend) - Controls UI features
2. **API Scope Permissions** (Backend) - Controls API access  
3. **Plan Configuration Limits** (Backend) - **NOT ENFORCED** ⚠️

**Critical Issue**: Plan limits are defined but never enforced at runtime, creating a security and business logic gap.

## Current Architecture Analysis

### Layer 1: Plan-based Permissions (Frontend)
**Location**: `src/frontend/composables/usePermissions.ts`
**Purpose**: Control UI feature visibility based on subscription plans
**Data Source**: `/api/billing/info` endpoint

```typescript
// Controls what users can see/interact with
export type PlanPermission = 
  | 'custom_headers'
  | 'priority_support' 
  | 'email_analytics'
  | 'custom_integrations'
  | 'sla_guarantee'
  | 'premium_delivery'
```

### Layer 2: API Scope Permissions (Backend)
**Location**: `src/backend/services/auth/scope-validator.service.ts`
**Purpose**: Control granular API access for operations
**Pattern**: Resource:Action (e.g., `domains:read`, `aliases:write`)

```typescript
// Controls what users can DO via API
'domains:read', 'domains:write', 'domains:config', 'domains:status'
'aliases:read', 'aliases:write', 'aliases:*'
'webhooks:read', 'webhooks:write', 'webhooks:*'
```

### Layer 3: Plan Configuration Limits (Backend) ⚠️ **CRITICAL GAP**
**Location**: `src/backend/services/billing/plan-config.service.ts`
**Purpose**: Define quantitative limits per plan
**Status**: **DEFINED BUT NOT ENFORCED**

```typescript
// Defined limits that are NOT being enforced:
free: { monthlyEmailLimit: 50, domains: 1, aliases: 2, webhooks: 2 }
pro: { monthlyEmailLimit: 1000, domains: 5, aliases: 50, webhooks: 50 }
enterprise: { monthlyEmailLimit: 10000, domains: 100, aliases: 500, webhooks: 100 }
```

## Critical Security & Business Issues

### 🚨 **Issue 1: Unenforced Plan Limits**
- Users can exceed their plan limits without restriction
- Free users could potentially create unlimited domains/aliases
- No enforcement at API endpoints for resource creation
- Business logic gap allowing revenue leakage

### 🚨 **Issue 2: Disconnected Permission Systems** 
- Plan permissions and API scopes operate independently
- User could have plan permission but lack API scope (or vice versa)
- No synchronization between UI capabilities and API access
- Inconsistent user experience

### 🚨 **Issue 3: Frontend Security Reliance**
- Plan permission checks only exist in frontend
- Backend API endpoints don't validate plan permissions
- Security through obscurity (hiding UI elements)
- Vulnerable to API manipulation

### 🚨 **Issue 4: Missing Enforcement Points**
Key API endpoints lack limit validation:
- `POST /api/domains` - No domain limit check
- `POST /api/aliases` - No alias limit check  
- `POST /api/webhooks` - No webhook limit check
- Email sending - Only basic monthly limit check

## Evidence of Current Gaps

### Plan Config Service Has Validation Logic BUT It's Not Used
```typescript
// This exists but is never called at API endpoints:
static validateUsageForPlan(planType: string, currentUsage: {
  domains: number;
  webhooks: number; 
  aliases: number;
}): { valid: boolean; violations: string[] }
```

### Usage Calculation Service Tracks BUT Doesn't Enforce
```typescript
// Calculates current usage but doesn't prevent new resource creation
const limits = PlanConfigService.getPlanLimits(user.planType, user._count.domains);
// Missing: Enforcement at creation time
```

## Recommended Solution: Unified Permission Architecture

### Phase 1: Immediate Security Fixes (High Priority)

#### 1.1 Add Plan Limit Enforcement Middleware
```typescript
// New: src/backend/middleware/plan-limits.middleware.ts
export const validatePlanLimits = async (request: FastifyRequest, reply: FastifyReply) => {
  const user = request.user;
  const operation = getOperationFromRoute(request);
  
  const currentUsage = await getCurrentUsage(user.id);
  const limits = PlanConfigService.getPlanLimits(user.planType, currentUsage.domains);
  
  // Enforce limits based on operation
  if (operation.type === 'create') {
    const validation = validateCreateOperation(operation.resource, currentUsage, limits);
    if (!validation.allowed) {
      return reply.code(403).send({
        error: 'Plan limit exceeded',
        message: validation.reason,
        upgradeRequired: true
      });
    }
  }
};
```

#### 1.2 Integrate Enforcement in API Routes
Apply middleware to creation endpoints:
- `POST /api/domains` 
- `POST /api/aliases`
- `POST /api/webhooks`

#### 1.3 Unified Permission Validation
```typescript
// New: src/backend/services/auth/unified-permissions.service.ts
export class UnifiedPermissionsService {
  static async validateAction(
    userId: string, 
    action: string, 
    resource: string
  ): Promise<PermissionResult> {
    // Check all three layers:
    // 1. API Scopes
    // 2. Plan Permissions  
    // 3. Plan Limits
    
    const [scopeCheck, planCheck, limitCheck] = await Promise.all([
      this.validateApiScopes(userId, action, resource),
      this.validatePlanPermissions(userId, action),
      this.validatePlanLimits(userId, action, resource)
    ]);
    
    return {
      allowed: scopeCheck.allowed && planCheck.allowed && limitCheck.allowed,
      violations: [...scopeCheck.violations, ...planCheck.violations, ...limitCheck.violations]
    };
  }
}
```

### Phase 2: Architecture Unification (Medium Priority)

#### 2.1 Unified Permission Types
```typescript
// New unified type system
export interface Permission {
  type: 'feature' | 'action' | 'limit';
  resource: string;
  action?: string;
  planRequirement: PlanType[];
  quantityLimit?: number;
}

// Unified permission definitions
export const PERMISSIONS: Record<string, Permission> = {
  'custom_headers': {
    type: 'feature',
    resource: 'webhooks',
    planRequirement: ['pro', 'enterprise']
  },
  'domain_create': {
    type: 'action', 
    resource: 'domains',
    action: 'create',
    planRequirement: ['free', 'pro', 'enterprise'],
    quantityLimit: (planType) => getPlanLimits(planType).domains
  }
};
```

#### 2.2 Frontend-Backend Synchronization
```typescript
// Enhanced frontend composable
export function usePermissions() {
  // Fetch unified permissions that include all three layers
  const fetchPermissions = async () => {
    const response = await fetch('/api/permissions/unified');
    return response.json(); // Includes features, actions, and current usage vs limits
  };
  
  // Real-time limit checking
  const canCreateResource = computed(() => (resource: string) => {
    const usage = currentUsage.value;
    const limits = planLimits.value;
    return usage[resource] < limits[resource];
  });
}
```

#### 2.3 Real-time Limit Updates
- WebSocket/SSE for real-time usage updates
- Progressive UI disabling as limits approached
- Upgrade prompts at appropriate times

### Phase 3: Enhanced User Experience (Low Priority)

#### 3.1 Proactive Limit Management
- Show usage bars in UI (e.g., "2/5 domains used")
- Warning notifications at 80% usage
- Graceful upgrade flows

#### 3.2 Permission Debugging Tools
- Admin panel showing all three permission layers
- User permission debugging endpoint for support
- Clear audit logs for permission decisions

## Implementation Priority

### 🔥 **Immediate (This Sprint)**
1. ✅ Document current issues (this document)
2. 🚨 Add plan limit enforcement middleware
3. 🚨 Apply enforcement to creation endpoints
4. 🚨 Add proper error responses with upgrade prompts

### 📋 **Next Sprint**
1. Unified permission validation service
2. Frontend-backend permission synchronization
3. Enhanced error handling and user messaging

### 📅 **Future Iterations**
1. Real-time usage updates
2. Enhanced UI with usage indicators  
3. Permission debugging tools
4. Advanced upgrade flows

## Risk Assessment

### High Risk (Current State)
- **Security**: Users can exceed plan limits via direct API calls
- **Revenue**: Free users potentially using paid features
- **Support**: Confusion from inconsistent permission behavior

### Medium Risk (During Migration)  
- **Complexity**: Three-layer system increases debugging difficulty
- **Performance**: Additional permission checks per request
- **Testing**: Complex permission matrix to validate

### Low Risk (After Implementation)
- **Maintenance**: Unified system easier to maintain
- **Scalability**: Clear patterns for adding new permissions
- **Security**: Defense in depth with multiple validation layers

## Testing Strategy

### Unit Tests
- Plan limit validation logic
- Permission computation accuracy
- Edge cases (exactly at limit, over limit)

### Integration Tests  
- API endpoint enforcement
- Frontend-backend permission sync
- Upgrade/downgrade scenarios

### Security Tests
- Bypassing frontend restrictions
- Direct API manipulation attempts
- Permission escalation scenarios

## Success Metrics

### Security Metrics
- ✅ Zero users exceeding plan limits
- ✅ All API endpoints validate permissions
- ✅ Consistent frontend/backend behavior

### Business Metrics  
- 📈 Increased conversion from limit enforcement
- 📉 Reduced support tickets about permissions
- 📈 Clear upgrade path adoption

### Technical Metrics
- ⚡ Permission check performance < 10ms
- 🐛 Zero permission-related bugs
- 📊 100% permission decision auditability

---

**Status**: 🚨 **CRITICAL PRIORITY**  
**Owner**: Backend & Frontend Teams  
**Timeline**: Phase 1 completion within 2 sprints  
**Last Updated**: 2025-06-23

