import { describe, test, expect } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
} from '../../setup/test-db-setup';
import { PlanConfigService } from '../../../src/backend/services/billing/plan-config.service';
import { UserAuthService } from '../../../src/backend/services/auth/user-auth.service';

describe('Plan Management', () => {
  setupTestDatabase();

  const userAuthService = new UserAuthService();

  test('should get plan configuration for all plan types', () => {
    const freePlan = PlanConfigService.getPlanConfig('free');
    expect(freePlan.name).toBe('Free');
    expect(freePlan.monthlyEmailLimit).toBe(freePlan.monthlyEmailLimit); // Use actual config value
    expect(freePlan.features).toContain(`Up to ${freePlan.monthlyEmailLimit} emails per month`);

    const proPlan = PlanConfigService.getPlanConfig('pro');
    expect(proPlan.name).toBe('Pro');
    expect(proPlan.monthlyEmailLimit).toBe(proPlan.monthlyEmailLimit); // Use actual config value
    expect(proPlan.price).toBeDefined();

    const enterprisePlan = PlanConfigService.getPlanConfig('enterprise');
    expect(enterprisePlan.name).toBe('Enterprise');
    expect(enterprisePlan.monthlyEmailLimit).toBe(enterprisePlan.monthlyEmailLimit); // Use actual config value
  });

  test('should throw error for unknown plan type', () => {
    expect(() => {
      PlanConfigService.getPlanConfig('unknown');
    }).toThrow('Unknown plan type: unknown');
  });

  test('should get plan limits correctly', () => {
    const freeConfig = PlanConfigService.getPlanConfig('free');
    const freeLimits = PlanConfigService.getPlanLimits('free');
    expect(freeLimits.monthlyEmails).toBe(freeConfig.monthlyEmailLimit);
    expect(freeLimits.domains).toBe(freeConfig.domains);
    expect(freeLimits.webhooks).toBe(freeConfig.webhooks);
    expect(freeLimits.aliases).toBe(freeConfig.aliases); // Should be 2 (catch-all + 1 additional)

    const proConfig = PlanConfigService.getPlanConfig('pro');
    const proLimits = PlanConfigService.getPlanLimits('pro');
    expect(proLimits.monthlyEmails).toBe(proConfig.monthlyEmailLimit);
    expect(proLimits.domains).toBe(proConfig.domains);
    expect(proLimits.webhooks).toBe(proConfig.webhooks); // For 1 domain (default)
    expect(proLimits.aliases).toBe(proConfig.aliases); // For 1 domain (default)
  });

  test('should validate plan upgrades and downgrades', () => {
    expect(PlanConfigService.isValidUpgrade('free', 'pro')).toBe(true);
    expect(PlanConfigService.isValidUpgrade('pro', 'enterprise')).toBe(true);
    expect(PlanConfigService.isValidUpgrade('free', 'enterprise')).toBe(true);

    expect(PlanConfigService.isValidUpgrade('pro', 'free')).toBe(false);
    expect(PlanConfigService.isValidUpgrade('enterprise', 'pro')).toBe(false);

    expect(PlanConfigService.isValidDowngrade('pro', 'free')).toBe(true);
    expect(PlanConfigService.isValidDowngrade('enterprise', 'pro')).toBe(true);
    expect(PlanConfigService.isValidDowngrade('free', 'pro')).toBe(false);
  });

  test('should validate usage against plan limits', () => {
    const freeConfig = PlanConfigService.getPlanConfig('free');

    // Valid usage for free plan (within limits)
    const validUsage = {
      domains: freeConfig.domains - 1,
      webhooks: freeConfig.webhooks - 1,
      aliases: freeConfig.aliases - 2
    };
    const validation = PlanConfigService.validateUsageForPlan('free', validUsage);
    expect(validation.valid).toBe(true);
    expect(validation.violations).toHaveLength(0);

    // Invalid usage for free plan (exceeds limits)
    const invalidUsage = {
      domains: freeConfig.domains + 2,
      webhooks: freeConfig.webhooks + 2,
      aliases: freeConfig.aliases + 5
    };
    const invalidValidation = PlanConfigService.validateUsageForPlan('free', invalidUsage);
    expect(invalidValidation.valid).toBe(false);
    expect(invalidValidation.violations.length).toBeGreaterThan(0);
  });

  test('should update user plan successfully', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'free',
      monthlyEmailLimit: 50,
    });

    const result = await userAuthService.updateUserPlan(testUser.id, 'pro');
    expect(result.success).toBe(true);
    expect(result.user?.planType).toBe('pro');
    expect(result.user?.monthlyEmailLimit).toBe(1000);

    // Verify in database
    const updatedUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { planType: true, monthlyEmailLimit: true }
    });
    expect(updatedUser?.planType).toBe('pro');
    expect(updatedUser?.monthlyEmailLimit).toBe(1000);
  });

  test('should prevent plan change when usage exceeds limits', async () => {
    const proConfig = PlanConfigService.getPlanConfig('pro');
    const freeConfig = PlanConfigService.getPlanConfig('free');

    const testUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'pro',
      monthlyEmailLimit: proConfig.monthlyEmailLimit,
    });

    // Create more domains than free plan allows
    const webhook = await prisma.webhook.create({
      data: {
        name: 'Test Webhook',
        url: 'https://example.com/webhook',
        userId: testUser.id
      }
    });

    // Create more domains than free plan allows
    const domainsToCreate = freeConfig.domains + 1;
    for (let i = 0; i < domainsToCreate; i++) {
      await prisma.domain.create({
        data: {
          domain: `test-domain-${i}.com`,
          userId: testUser.id
        }
      });
    }

    const result = await userAuthService.updateUserPlan(testUser.id, 'free');
    expect(result.success).toBe(false);
    expect(result.error).toContain('Too many domains');
  });

  test('should get user plan info correctly', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'pro',
      monthlyEmailLimit: 1000,
      currentMonthEmails: 150,
    });

    const webhook = await prisma.webhook.create({
      data: {
        name: 'Test Webhook',
        url: 'https://example.com/webhook',
        userId: testUser.id
      }
    });

    await prisma.domain.create({
      data: {
        domain: 'test-info.com',
        userId: testUser.id
      }
    });

    const proConfig = PlanConfigService.getPlanConfig('pro');
    const planInfo = await userAuthService.getUserPlanInfo(testUser.id);
    expect(planInfo).toBeDefined();
    expect(planInfo?.planConfig.name).toBe(proConfig.name);
    expect(planInfo?.usage.emails).toBe(150);
    expect(planInfo?.usage.domains).toBe(1);
    // expect(planInfo?.usage.webhooks).toBe(1); // TODO: Update for new architecture
    expect(planInfo?.limits.emails).toBe(proConfig.monthlyEmailLimit);
    expect(planInfo?.limits.domains).toBe(proConfig.domains);
  });

  test('should handle non-existent user gracefully', async () => {
    const result = await userAuthService.updateUserPlan('non-existent-id', 'pro');
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not found');

    const planInfo = await userAuthService.getUserPlanInfo('non-existent-id');
    expect(planInfo).toBeNull();
  });

  test('should handle invalid plan type in update', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      planType: 'free',
    });

    const result = await userAuthService.updateUserPlan(testUser.id, 'invalid-plan');
    expect(result.success).toBe(false);
    expect(result.error).toContain('Unknown plan type');
  });
});
