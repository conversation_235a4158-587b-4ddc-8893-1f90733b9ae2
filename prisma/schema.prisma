generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model User {
  id                 String              @id @default(cuid())
  email              String              @unique
  password           String
  name               String?
  verified           Boolean             @default(false)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  monthlyEmailLimit  Int                 @default(50)
  currentMonthEmails Int                 @default(0)
  lastUsageReset     DateTime            @default(now())
  planType           String              @default("free")
  apiKeys            ApiKey[]
  creditBatches      CreditBatch[]
  creditTransactions CreditTransaction[]
  domains            Domain[]
  invoices           Invoice[]
  paymentMethods     PaymentMethod[]
  payments           Payment[]
  subscriptions      Subscription[]
  webhooks           Webhook[]

  @@map("users")
}

model Webhook {
  id            String   @id @default(cuid())
  userId        String
  name          String
  url           String
  description   String?
  active        Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  verified      Bo<PERSON>an  @default(false)
  webhookSecret String?
  customHeaders Json?
  aliases       Alias[]
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("webhooks")
}

model Domain {
  id                       String             @id @default(cuid())
  userId                   String
  domain                   String             @unique
  active                   Boolean            @default(true)
  verified                 Boolean            @default(false)
  verificationStatus       VerificationStatus @default(PENDING)
  lastVerificationAttempt  DateTime?
  nextVerificationCheck    DateTime?
  verificationFailureCount Int                @default(0)
  verificationToken        String?
  dataRetentionDays        Int                @default(30)
  createdAt                DateTime           @default(now())
  updatedAt                DateTime           @updatedAt
  configuration            Json?
  aliases                  Alias[]
  user                     User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  emails                   Email[]

  @@map("domains")
}

model Alias {
  id            String   @id @default(cuid())
  domainId      String
  webhookId     String
  email         String
  active        Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  configuration Json?
  domain        Domain   @relation(fields: [domainId], references: [id], onDelete: Cascade)
  webhook       Webhook  @relation(fields: [webhookId], references: [id])

  @@unique([email, domainId])
  @@map("aliases")
}

model Email {
  id               String         @id @default(cuid())
  domainId         String?
  messageId        String         @unique
  fromAddress      String
  toAddresses      String[]
  subject          String?
  deliveryStatus   DeliveryStatus @default(PENDING)
  deliveryAttempts Int            @default(0)
  lastAttemptAt    DateTime?
  deliveredAt      DateTime?
  errorMessage     String?
  expiresAt        DateTime
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  isTestWebhook    Boolean        @default(false)
  webhookPayload   Json?
  domain           Domain?        @relation(fields: [domainId], references: [id], onDelete: Cascade)

  @@map("emails")
}

model AuditLog {
  id           String   @id @default(cuid())
  action       String
  resourceId   String?
  resourceType String?
  metadata     Json?
  ipAddress    String?
  userAgent    String?
  createdAt    DateTime @default(now())
  expiresAt    DateTime

  @@map("audit_logs")
}

model Subscription {
  id                   String             @id @default(cuid())
  mollieId             String             @unique
  status               SubscriptionStatus @default(PENDING)
  planType             String
  interval             String
  amount               Decimal            @db.Decimal(10, 2)
  currency             String             @default("EUR")
  description          String?
  mollieCustomerId     String?
  molliePaymentMethod  String?
  startDate            DateTime?
  nextPaymentDate      DateTime?
  cancelledAt          DateTime?
  cancelReason         String?
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  userId               String
  grandfatheredAt      DateTime?
  grandfatheredPrice   Decimal?           @db.Decimal(10, 2)
  grandfatheringReason String?
  isGrandfathered      Boolean            @default(false)
  payments             Payment[]
  user                 User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model Payment {
  id                  String        @id @default(cuid())
  mollieId            String        @unique
  status              PaymentStatus @default(PENDING)
  amount              Decimal       @db.Decimal(10, 2)
  currency            String        @default("EUR")
  description         String?
  method              String?
  paidAt              DateTime?
  cancelledAt         DateTime?
  expiredAt           DateTime?
  failedAt            DateTime?
  failureReason       String?
  mollieCustomerId    String?
  molliePaymentMethod String?
  mollieWebhookData   Json?
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt
  userId              String
  subscriptionId      String?
  creditBatches       CreditBatch[]
  invoices            Invoice[]
  subscription        Subscription? @relation(fields: [subscriptionId], references: [id])
  user                User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payments")
}

model Invoice {
  id            String   @id @default(cuid())
  invoiceNumber String   @unique
  pdfPath       String?
  generatedAt   DateTime @default(now())
  paymentId     String
  userId        String
  amount        Decimal  @db.Decimal(10, 2)
  currency      String   @default("EUR")
  description   String
  billingPeriod String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  payment       Payment  @relation(fields: [paymentId], references: [id], onDelete: Cascade)
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("invoices")
}

model PaymentMethod {
  id              String   @id @default(cuid())
  mollieId        String   @unique
  type            String
  description     String?
  isDefault       Boolean  @default(false)
  cardHolder      String?
  cardNumber      String?
  cardExpiryDate  String?
  cardFingerprint String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  userId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payment_methods")
}

model ApiKey {
  id          String    @id @default(cuid())
  name        String
  keyHash     String    @unique
  keyPrefix   String
  lastUsedAt  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  userId      String
  description String?
  scopes      Json      @default("[\"*\"]")
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model CreditBatch {
  id                String              @id @default(cuid())
  userId            String
  amount            Int
  remainingAmount   Int
  purchasedAt       DateTime            @default(now())
  expiresAt         DateTime
  paymentId         String?
  isExpired         Boolean             @default(false)
  originalExpiresAt DateTime?
  extensionCount    Int                 @default(0)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  payment           Payment?            @relation(fields: [paymentId], references: [id])
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions      CreditTransaction[]

  @@index([userId])
  @@index([expiresAt])
  @@index([isExpired])
  @@map("credit_batches")
}

model CreditTransaction {
  id          String       @id @default(cuid())
  userId      String
  batchId     String?
  type        String
  amount      Int
  description String?
  createdAt   DateTime     @default(now())
  batch       CreditBatch? @relation(fields: [batchId], references: [id])
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([batchId])
  @@index([type])
  @@map("credit_transactions")
}

enum DeliveryStatus {
  PENDING
  DELIVERED
  FAILED
  RETRYING
  EXPIRED
}

enum VerificationStatus {
  PENDING
  VERIFIED
  ACTIVE
  WARNING
  SUSPENDED
  FAILED
}

enum SubscriptionStatus {
  PENDING
  ACTIVE
  SUSPENDED
  CANCELLED
  COMPLETED
}

enum PaymentStatus {
  PENDING
  PAID
  CANCELLED
  EXPIRED
  FAILED
  AUTHORIZED
  REFUNDED
  PARTIALLY_REFUNDED
}
