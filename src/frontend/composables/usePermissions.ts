import { ref, computed } from 'vue'

// Permission types matching backend
export type PlanPermission = 
  | 'custom_headers'
  | 'priority_support'
  | 'email_analytics'
  | 'custom_integrations'
  | 'sla_guarantee'
  | 'premium_delivery'

// Shared state for user permissions
const userPermissions = ref<PlanPermission[]>([])
const userPlanType = ref<string>('free')
const isLoading = ref(false)
const lastUpdated = ref<Date | null>(null)
const error = ref<string | null>(null)

// Fetch user permissions from billing info
const fetchUserPermissions = async () => {
  if (isLoading.value) return // Prevent duplicate calls

  try {
    isLoading.value = true
    error.value = null
    
    const response = await fetch('/api/billing/info', { credentials: 'include' })
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    if (data.success) {
      userPermissions.value = data.data.currentPlan.permissions || []
      userPlanType.value = data.data.currentPlan.type || 'free'
      lastUpdated.value = new Date()
    } else {
      throw new Error('Invalid response format')
    }
    
    return data
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load user permissions'
    console.error('Failed to load user permissions:', err)
    throw err
  } finally {
    isLoading.value = false
  }
}

// Composable hook
export function usePermissions() {
  // Permission checking functions
  const hasPermission = (permission: PlanPermission): boolean => {
    return userPermissions.value.includes(permission)
  }

  const hasAnyPermission = (permissions: PlanPermission[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: PlanPermission[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  const getMissingPermissions = (requiredPermissions: PlanPermission[]): PlanPermission[] => {
    return requiredPermissions.filter(permission => !hasPermission(permission))
  }

  // Computed values for common permissions
  const canUseCustomHeaders = computed(() => hasPermission('custom_headers'))
  const hasPrioritySupport = computed(() => hasPermission('priority_support'))
  const hasEmailAnalytics = computed(() => hasPermission('email_analytics'))
  const hasCustomIntegrations = computed(() => hasPermission('custom_integrations'))
  const hasSlaGuarantee = computed(() => hasPermission('sla_guarantee'))
  const hasPremiumDelivery = computed(() => hasPermission('premium_delivery'))

  // Load permissions if not already loaded
  const loadPermissions = async () => {
    if (!userPermissions.value.length || (lastUpdated.value && Date.now() - lastUpdated.value.getTime() > 300000)) {
      // Load if no data or data is older than 5 minutes
      await fetchUserPermissions()
    }
    return userPermissions.value
  }

  // Force refresh - bypasses cache completely
  const refreshPermissions = async () => {
    // Reset lastUpdated to force a fresh fetch
    lastUpdated.value = null
    await fetchUserPermissions()
    return userPermissions.value
  }

  return {
    // State
    userPermissions: computed(() => userPermissions.value),
    userPlanType: computed(() => userPlanType.value),
    isLoading: computed(() => isLoading.value),
    lastUpdated: computed(() => lastUpdated.value),
    error: computed(() => error.value),
    
    // Permission checking functions
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getMissingPermissions,
    
    // Computed permission checks
    canUseCustomHeaders,
    hasPrioritySupport,
    hasEmailAnalytics,
    hasCustomIntegrations,
    hasSlaGuarantee,
    hasPremiumDelivery,
    
    // Methods
    loadPermissions,
    refreshPermissions
  }
}
