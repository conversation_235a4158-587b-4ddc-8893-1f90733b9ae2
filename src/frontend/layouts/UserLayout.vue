<template>
  <div class="min-h-screen bg-base-200/40">
    <!-- User Header Component -->
    <UserHeader />

    <!-- Dashboard Content with Metrics and Navigation -->
    <div class="bg-base-100 overflow-x-hidden">
      <!-- Metrics Pill and Tab Navigation (only for dashboard pages) -->
      <template v-if="!isSettingsPage">
        <!-- Metrics Pill -->
        <MetricsPill />

        <!-- Tab Navigation -->
        <TabNavigation
          :counts="counts"
          @create-action="handleCreateAction"
        />
      </template>

      <!-- Main Content -->
      <main class="bg-base-200/40 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 pb-8">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import UserHeader from '../components/ui/UserHeader.vue'
import TabNavigation from '../components/dashboard/TabNavigation.vue'
import MetricsPill from '../components/dashboard/MetricsPill.vue'
import { useMetrics } from '@composables/useMetrics'
import { useOnboarding } from '@composables/useOnboarding'
import { useWebSocket } from '@composables/useWebSocket'

// Vue Router
const route = useRoute()

// Use shared metrics composable instead of duplicate API call
const { counts, loadMetrics } = useMetrics()

// Use onboarding state to hide navigation during onboarding
const { shouldShowOnboarding } = useOnboarding()

// Initialize WebSocket for real-time updates (only for authenticated users)
// Since this is UserLayout, we know we're in an authenticated context
const { isConnected, connectionError } = useWebSocket()

// Detect if we're on the settings page using Vue Router
const isSettingsPage = computed(() => {
  return route.name === 'settings'
})

// Load dashboard data - now uses shared composable
const loadDashboardData = async () => {
  try {
    await loadMetrics()
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
}

// Handle create actions from tab navigation
const handleCreateAction = (actionType: string) => {
  // Use the global modal system set up by App
  const tryOpenModal = () => {
    if ((window as any).openModal) {
      (window as any).openModal(actionType)
      return true
    } else if ((window as any).vueModal) {
      (window as any).vueModal.open(actionType)
      return true
    }
    return false
  }

  // Try immediately, if not available, wait a bit and try again
  if (!tryOpenModal()) {
    console.warn('Modal system not available yet, retrying...')
    setTimeout(() => {
      if (!tryOpenModal()) {
        console.error('Modal system still not available after retry')
      }
    }, 100)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
/* User layout specific styles */
</style>
