import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { PlanConfigService } from '../billing/plan-config.service.js';

export interface CreateWebhookData {
  name: string;
  url: string;
  description?: string;
  active?: boolean;
  webhookSecret?: string;
  generateSecret?: boolean;
  customHeaders?: Record<string, string>;
  userId: string;
}

export interface UpdateWebhookData {
  name?: string;
  url?: string;
  description?: string;
  active?: boolean;
  webhookSecret?: string;
  removeSecret?: boolean;
  generateSecret?: boolean;
  customHeaders?: Record<string, string>;
}

export class WebhookService {
  /**
   * Get all webhooks for a user
   */
  async getUserWebhooks(userId: string) {
    const webhooks = await prisma.webhook.findMany({
      where: { userId },
      include: {
        _count: {
          select: {
            aliases: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return webhooks.map(webhook => ({
      id: webhook.id,
      name: webhook.name,
      url: webhook.url,
      description: webhook.description,
      active: webhook.active,
      verified: webhook.verified,
      hasSecret: !!webhook.webhookSecret,
      customHeaders: webhook.customHeaders as Record<string, string> | null,
      createdAt: webhook.createdAt.toISOString(),
      updatedAt: webhook.updatedAt.toISOString(),
      aliasCount: webhook._count.aliases
    }));
  }

  /**
   * Get a specific webhook by ID for a user
   */
  async getWebhookById(webhookId: string, userId: string) {
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      },
      include: {
        aliases: {
          select: {
            id: true,
            email: true
          }
        }
      }
    });

    if (!webhook) {
      return null;
    }

    return {
      id: webhook.id,
      name: webhook.name,
      url: webhook.url,
      description: webhook.description,
      active: webhook.active,
      verified: webhook.verified,
      hasSecret: !!webhook.webhookSecret,
      customHeaders: webhook.customHeaders as Record<string, string> | null,
      createdAt: webhook.createdAt.toISOString(),
      updatedAt: webhook.updatedAt.toISOString(),
      aliases: webhook.aliases
    };
  }

  /**
   * Create a new webhook
   */
  async createWebhook(data: CreateWebhookData) {
    // Check if webhook URL already exists for this user
    const existingWebhook = await prisma.webhook.findFirst({
      where: {
        url: data.url,
        userId: data.userId
      }
    });

    if (existingWebhook) {
      throw new Error('Webhook with this URL already exists');
    }

    // Validate custom headers for plan restrictions
    if (data.customHeaders && Object.keys(data.customHeaders).length > 0) {
      const user = await prisma.user.findUnique({
        where: { id: data.userId },
        select: { planType: true }
      });

      if (!PlanConfigService.userHasPermission(user?.planType || 'free', 'custom_headers')) {
        throw new Error('Custom headers require Pro or Enterprise plan');
      }
    }

    // Handle webhook secret
    let finalWebhookSecret: string | null = null;
    if (data.generateSecret) {
      const crypto = await import('crypto');
      finalWebhookSecret = crypto.randomBytes(32).toString('hex');
    } else if (data.webhookSecret) {
      finalWebhookSecret = data.webhookSecret;
    }

    const webhook = await prisma.webhook.create({
      data: {
        name: data.name,
        url: data.url,
        description: data.description || null,
        active: data.active ?? true,
        webhookSecret: finalWebhookSecret,
        customHeaders: data.customHeaders || null,
        verified: false, // New webhooks start unverified
        userId: data.userId
      }
    });

    return {
      webhook: {
        id: webhook.id,
        name: webhook.name,
        url: webhook.url,
        description: webhook.description,
        active: webhook.active,
        verified: webhook.verified,
        hasSecret: !!webhook.webhookSecret,
        customHeaders: webhook.customHeaders as Record<string, string> | null,
        createdAt: webhook.createdAt.toISOString(),
        updatedAt: webhook.updatedAt.toISOString()
      },
      generatedSecret: data.generateSecret ? finalWebhookSecret : undefined
    };
  }

  /**
   * Update an existing webhook
   */
  async updateWebhook(webhookId: string, userId: string, updates: UpdateWebhookData) {
    // Check if webhook exists and belongs to user
    const existingWebhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!existingWebhook) {
      throw new Error('Webhook not found');
    }

    // Validate custom headers for plan restrictions
    if (updates.customHeaders && Object.keys(updates.customHeaders).length > 0) {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { planType: true }
      });

      if (!PlanConfigService.userHasPermission(user?.planType || 'free', 'custom_headers')) {
        throw new Error('Custom headers require Pro or Enterprise plan');
      }
    }

    // If URL is being updated, check for conflicts
    if (updates.url && updates.url !== existingWebhook.url) {
      const urlConflict = await prisma.webhook.findFirst({
        where: {
          url: updates.url,
          userId,
          id: { not: webhookId }
        }
      });

      if (urlConflict) {
        throw new Error('Another webhook with this URL already exists');
      }
    }

    // Handle webhook secret updates
    let webhookSecretUpdate: string | null | undefined = undefined;
    let generatedSecret: string | undefined = undefined;

    if (updates.removeSecret) {
      webhookSecretUpdate = null;
    } else if (updates.generateSecret) {
      const crypto = await import('crypto');
      generatedSecret = crypto.randomBytes(32).toString('hex');
      webhookSecretUpdate = generatedSecret;
    } else if (updates.webhookSecret !== undefined) {
      webhookSecretUpdate = updates.webhookSecret;
    }

    // Prepare update data
    const updateData: any = {
      ...updates,
      updatedAt: new Date()
    };

    // Remove secret-related fields from the main update
    delete updateData.webhookSecret;
    delete updateData.removeSecret;
    delete updateData.generateSecret;

    // Add webhook secret if it was modified
    if (webhookSecretUpdate !== undefined) {
      updateData.webhookSecret = webhookSecretUpdate;
    }

    // Reset verification status if URL or secret changed
    if (updates.url && updates.url !== existingWebhook.url) {
      updateData.verified = false;
      updateData.active = false; // Also set to inactive when URL changes
    } else if (webhookSecretUpdate !== undefined && webhookSecretUpdate !== existingWebhook.webhookSecret) {
      updateData.verified = false;
    }

    const updatedWebhook = await prisma.webhook.update({
      where: { id: webhookId },
      data: updateData
    });

    return {
      webhook: {
        id: updatedWebhook.id,
        name: updatedWebhook.name,
        url: updatedWebhook.url,
        description: updatedWebhook.description,
        active: updatedWebhook.active,
        verified: updatedWebhook.verified,
        hasSecret: !!updatedWebhook.webhookSecret,
        customHeaders: updatedWebhook.customHeaders as Record<string, string> | null,
        createdAt: updatedWebhook.createdAt.toISOString(),
        updatedAt: updatedWebhook.updatedAt.toISOString()
      },
      generatedSecret
    };
  }

  /**
   * Delete a webhook
   */
  async deleteWebhook(webhookId: string, userId: string) {
    // Check if webhook exists and belongs to user
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      },
      include: {
        _count: {
          select: {
            aliases: true
          }
        }
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Check if webhook is in use
    if (webhook._count.aliases > 0) {
      throw new Error('Cannot delete webhook that is currently in use by aliases');
    }

    await prisma.webhook.delete({
      where: { id: webhookId }
    });

    return { success: true };
  }

  /**
   * Verify a webhook
   */
  async verifyWebhook(webhookId: string, userId: string) {
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Generate verification token from last 5 characters of webhook ID
    const verificationToken = webhookId.slice(-5);
    const timestamp = Math.floor(Date.now() / 1000);

    const verificationPayload = {
      type: 'webhook_verification',
      verification_token: verificationToken,
      timestamp,
      webhook: {
        id: webhook.id,
        url: webhook.url
      }
    };

    // Send verification request to webhook
    const { queueWebhookDelivery } = await import('../queue.js');
    const jobId = await queueWebhookDelivery(
      webhook.url,
      verificationPayload as any,
      webhook.webhookSecret || undefined,
      webhook.customHeaders as Record<string, string> || undefined
    );

    logger.info({
      webhookId,
      webhookUrl: webhook.url,
      verificationToken,
      jobId
    }, 'Webhook verification request sent');

    // Note: The webhook is not marked as verified here
    // It should be verified when the user enters the correct token
    // or when we implement a callback endpoint for automatic verification

    return {
      id: webhook.id,
      verified: webhook.verified,
      verificationSent: true,
      verificationToken, // Return for frontend to validate
      jobId: jobId?.toString()
    };
  }

  /**
   * Complete webhook verification with token
   */
  async completeWebhookVerification(webhookId: string, userId: string, providedToken: string) {
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Generate expected token from webhook ID
    const expectedToken = webhookId.slice(-5);

    // Case-insensitive comparison to allow uppercase input
    if (providedToken.toLowerCase() !== expectedToken.toLowerCase()) {
      throw new Error('Invalid verification token');
    }

    // Mark webhook as verified
    const updatedWebhook = await prisma.webhook.update({
      where: { id: webhookId },
      data: { verified: true }
    });

    logger.info({
      webhookId,
      webhookUrl: webhook.url
    }, 'Webhook verification completed successfully');

    return {
      id: updatedWebhook.id,
      verified: updatedWebhook.verified,
      verificationCompleted: true
    };
  }

  /**
   * Test webhook with mock email payload
   */
  async testWebhook(webhookId: string, userId: string) {
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Only allow testing verified webhooks
    if (!webhook.verified) {
      throw new Error('Webhook must be verified before testing');
    }

    // Find domain/alias context for this webhook to make test realistic
    const webhookContext = await this.findWebhookContext(webhookId, userId);

    // Generate a unique message ID for the test
    const testMessageId = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const testTimestamp = new Date().toISOString();

    // Generate mock payload using the same structure as EmailParser
    const mockPayload = this.generateMockEmailPayload(testMessageId, testTimestamp, userId, webhookContext);

    // Create email record in database marked as test
    const emailRecord = await prisma.email.create({
      data: {
        messageId: testMessageId,
        fromAddress: mockPayload.message.sender.email,
        toAddresses: [mockPayload.message.recipient.email],
        subject: mockPayload.message.subject,
        deliveryStatus: 'PENDING',
        deliveryAttempts: 0,
        isTestWebhook: true,
        webhookPayload: mockPayload,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        domainId: webhookContext?.domainId || null, // Include domain context for realistic testing
      }
    });

    // Queue webhook delivery
    const { queueWebhookDelivery } = await import('../queue.js');
    const jobId = await queueWebhookDelivery(
      webhook.url,
      mockPayload as any,
      webhook.webhookSecret || undefined,
      webhook.customHeaders as Record<string, string> || undefined
    );

    logger.info({
      webhookId,
      webhookUrl: webhook.url,
      testMessageId,
      jobId,
      domainId: webhookContext?.domainId,
      aliasId: webhookContext?.aliasId
    }, 'Test webhook payload queued for delivery');

    return {
      webhook: {
        id: webhook.id,
        url: webhook.url,
        name: webhook.name
      },
      messageId: testMessageId,
      jobId: jobId?.toString(),
      sentAt: testTimestamp
    };
  }

  /**
   * Find the domain/alias context for a webhook to make test payloads realistic
   */
  private async findWebhookContext(webhookId: string, userId: string) {
    // First, check if any aliases use this webhook (more specific)
    // Use most recently created alias for predictable behavior
    const alias = await prisma.alias.findFirst({
      where: {
        webhookId: webhookId,
        active: true,
        domain: {
          userId: userId
        }
      },
      include: {
        domain: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (alias) {
      return {
        domainId: alias.domainId,
        aliasId: alias.id,
        aliasEmail: alias.email,
        domain: alias.domain.domain
      };
    }

    // No context found - webhook might not be in use yet
    return null;
  }

  /**
   * Generate a realistic mock email payload for testing
   */
  private generateMockEmailPayload(messageId: string, timestamp: string, userId: string, context?: any) {
    // For test clarity, always use test domain in email addresses but include real domainId for n8n compatibility
    const testDomain = "test.emailconnect.eu";
    const testEmail = `${userId.slice(-8)}@${testDomain}`;

    // Create the base payload structure
    const basePayload = {
      message: {
        sender: {
          name: "Test Sender",
          email: "<EMAIL>"
        },
        recipient: {
          name: "Test Recipient",
          email: testEmail
        },
        subject: "Test Email from EmailConnect",
        content: {
          text: "This is a test email sent from EmailConnect to verify your webhook is working correctly.\n\nIf you receive this, your webhook integration is functioning properly!",
          html: "<p>This is a <strong>test email</strong> sent from EmailConnect to verify your webhook is working correctly.</p><p>If you receive this, your webhook integration is functioning properly!</p>"
        },
        date: timestamp,
        attachments: []
      },
      envelope: {
        messageId: messageId,
        xMailer: "EmailConnect-Test/1.0",
        deliveredTo: testEmail,
        xOriginalTo: testEmail,
        returnPath: "<EMAIL>",
        allRecipients: {
          to: [testEmail],
          cc: [],
          bcc: []
        },
        headers: {
          "received": `from ${testDomain}`,
          "message-id": messageId,
          "date": timestamp,
          "from": "<EMAIL>",
          "to": testEmail,
          "subject": "Test Email from EmailConnect",
          "mime-version": "1.0",
          "content-type": "multipart/alternative",
          "x-test-webhook": "true"
        },
        processed: {
          timestamp: timestamp,
          domain: testDomain,
          alias: context?.aliasEmail || testEmail, // Use context alias or fallback to test email
          originalSize: 1024
        }
      }
    };

    // Add domain/alias context for n8n filtering compatibility
    if (context) {
      return {
        ...basePayload,
        domainId: context.domainId,
        aliasId: context.aliasId
      };
    }

    return basePayload;
  }
}
